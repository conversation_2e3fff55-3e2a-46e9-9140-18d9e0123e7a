import os
from urllib.parse import parse_qs

TEMPLATE_PATH = os.path.join(os.path.dirname(__file__), 'templates', 'home.html')

def load_template(path):
    with open(path, 'r') as file:
        return file.read()

def application(environ, start_response):
    path = environ.get('PATH_INFO', '/')

    if path.startswith('/hello/'):
        username = path.split('/hello/')[-1]
        template = load_template(TEMPLATE_PATH)
        content = template.replace('{{ username }}', username)

        status = '200 OK'
        headers = [('Content-type', 'text/html; charset=utf-8')]
        start_response(status, headers)
        return [content.encode('utf-8')]

    # 404 fallback
    start_response('404 NOT FOUND', [('Content-Type', 'text/plain')])
    return [b'Not Found']

if __name__ == '__main__':
    from wsgiref.simple_server import make_server

    print("Starting WSGI server on http://localhost:8000")
    print("Try: http://localhost:8000/hello/alice")

    server = make_server('localhost', 8000, application)
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped.")
