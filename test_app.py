#!/usr/bin/env python3

import app
from io import StringIO
import sys

def test_wsgi_app():
    """Test the WSGI application directly"""
    
    # Test the /hello/alice endpoint
    environ = {
        'REQUEST_METHOD': 'GET',
        'PATH_INFO': '/hello/alice',
        'SERVER_NAME': 'localhost',
        'SERVER_PORT': '8000',
        'wsgi.version': (1, 0),
        'wsgi.url_scheme': 'http',
        'wsgi.input': StringIO(),
        'wsgi.errors': sys.stderr,
        'wsgi.multithread': False,
        'wsgi.multiprocess': True,
        'wsgi.run_once': False
    }
    
    response_data = []
    status = None
    headers = None
    
    def start_response(status_arg, headers_arg):
        nonlocal status, headers
        status = status_arg
        headers = headers_arg
    
    # Call the application
    result = app.application(environ, start_response)
    
    print(f"Status: {status}")
    print(f"Headers: {headers}")
    print("Response:")
    for chunk in result:
        print(chunk.decode('utf-8'))

if __name__ == "__main__":
    test_wsgi_app()
