# WS<PERSON> Hello App

A simple WSGI web application that greets users with personalized hello messages.

## Features

- **Dynamic Greetings**: Displays personalized hello messages based on the username in the URL
- **Template Rendering**: Uses HTML templates with variable substitution
- **WSGI Compliant**: Can be deployed with any WSGI server
- **Standalone Server**: Includes built-in development server for easy testing

## Project Structure

```
wsgi_hello_app/
├── app.py              # Main WSGI application
├── templates/
│   └── home.html       # HTML template for greetings
├── test_app.py         # Direct WSGI testing script
└── README.md           # This file
```

## Usage

### Running the Application

**Method 1: Direct execution (Recommended)**
```bash
python app.py
```

**Method 2: Using Python's built-in WSGI server**
```bash
python -m wsgiref.simple_server app:application
```

The server will start on `http://localhost:8000`

### Testing the Application

Visit the following URLs in your browser:
- `http://localhost:8000/hello/alice` - <PERSON><PERSON>s "alice"
- `http://localhost:8000/hello/john` - <PERSON><PERSON>s "john"
- `http://localhost:8000/hello/[any-name]` - Greets any name you provide

### Direct Testing

Run the test script to verify functionality without starting a server:
```bash
python test_app.py
```

## Modifications Made

The original `app.py` was enhanced with the following additions:

1. **Built-in Development Server**: Added a `__main__` block that includes a WSGI server using `wsgiref.simple_server.make_server`
2. **User-friendly Output**: Added startup messages showing the server URL and example endpoints
3. **Graceful Shutdown**: Implemented keyboard interrupt handling for clean server shutdown

### Code Addition
```python
if __name__ == '__main__':
    from wsgiref.simple_server import make_server
    
    print("Starting WSGI server on http://localhost:8000")
    print("Try: http://localhost:8000/hello/alice")
    
    server = make_server('localhost', 8000, application)
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped.")
```

## How It Works

1. **URL Routing**: The application checks if the URL path starts with `/hello/`
2. **Username Extraction**: Extracts the username from the URL path after `/hello/`
3. **Template Loading**: Loads the HTML template from `templates/home.html`
4. **Variable Substitution**: Replaces `{{ username }}` in the template with the actual username
5. **Response**: Returns the rendered HTML with appropriate HTTP headers

## Requirements

- Python 3.x
- No external dependencies (uses only Python standard library)

## Deployment

This WSGI application can be deployed with any WSGI server such as:
- Gunicorn: `gunicorn app:application`
- uWSGI: `uwsgi --http :8000 --wsgi-file app.py --callable application`
- Apache with mod_wsgi
- Nginx with uWSGI/Gunicorn

## Testing Results

✅ **Verified Endpoints:**
- `/hello/alice` → Returns "Hello, alice!" page
- `/hello/john` → Returns "Hello, john!" page
- Invalid paths → Returns 404 Not Found

The application has been tested and confirmed working with both direct execution and WSGI server methods.
